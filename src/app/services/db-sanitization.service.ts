import { Injectable } from '@angular/core';
import { PERMIT_HEADER } from '../data-models/data_classes';

@Injectable({
  providedIn: 'root'
})
export class DbSanitizationService {

  constructor() { }

  /**
   * Database schema field definitions for each table
   * These should match the fields defined in metadata.json
   */
  private readonly DB_SCHEMA_FIELDS = {
    // PERMIT related tables
    PERMIT_HEADER: [
      // Base DataStructure fields
      'LID', 'FID', 'OBJECT_STATUS', 'SYNC_STATUS',
      // PERMIT_HEADER specific fields from metadata.json
      'PERMIT_NO', 'PERMIT_TYPE', 'DESCRIPTION', 'FACILITY_ID', 'DIVISION_ID', 'TAG',
      'AGENT_ID_INT', 'AGENT_ID_EXT', 'JOB_NO', 'STATUS', 'REQUESTED_BY', 'REQUESTED_ON',
      'SOURCE', 'PERMIT_DATE', 'EXPIRY_DATE', 'IS_EXTENDED', 'EXTENSION_DATE', 'COMMENTS',
      'APPROVAL_SUMMARY', 'P_MODE'
    ],
    PERMIT_STAKEHOLDER: [
      'LID', 'FID', 'OBJECT_STATUS', 'SYNC_STATUS',
      'PERMIT_NO', 'ROW_ID', 'ROLE', 'CONTEXT', 'AGENT_ID', 'USER_ID', 'COMMENT', 'P_MODE', 'IS_ACTIVE'
    ],
    PERMIT_FORM: [
      'LID', 'FID', 'OBJECT_STATUS', 'SYNC_STATUS',
      'PERMIT_NO', 'FORM_GUID', 'FORM_ID', 'FORM_NAME', 'FORM_VERSION', 'FORM_TITLE',
      'DATA', 'CHANGED_BY', 'CHANGED_ON', 'PARTIAL_FLAG', 'COMPLETED', 'EXTERNAL_URL', 'P_MODE'
    ],
    PERMIT_LOG: [
      'LID', 'FID', 'OBJECT_STATUS', 'SYNC_STATUS',
      'PERMIT_NO', 'LOG_NO', 'PERMIT_STATUS', 'APPROVAL', 'APPR_TYPE', 'CREATED_ON',
      'CREATED_BY', 'COMMENT', 'P_MODE', 'ACTION'
    ],
    PERMIT_DOC: [
      'LID', 'FID', 'OBJECT_STATUS', 'SYNC_STATUS',
      'PERMIT_NO', 'DOC_ID', 'DOC_TYPE', 'P_MODE'
    ]
  };

  /**
   * Sanitize any object by removing fields that don't exist in the database schema
   * @param tableName - The database table name
   * @param dataObject - The object to sanitize
   * @returns Sanitized object with only valid database fields
   */
  sanitizeForDB(tableName: string, dataObject: any): any {
    const allowedFields = this.DB_SCHEMA_FIELDS[tableName];
    
    if (!allowedFields) {
      console.warn(`DbSanitizationService: No schema definition found for table ${tableName}`);
      return dataObject; // Return original object if no schema defined
    }

    const sanitized: any = {};
    allowedFields.forEach(field => {
      if (dataObject.hasOwnProperty(field)) {
        sanitized[field] = dataObject[field];
      }
    });

    return sanitized;
  }

  /**
   * Specific method for PERMIT_HEADER sanitization
   * @param permitHeader - PERMIT_HEADER object to sanitize
   * @returns Sanitized object safe for database insert
   */
  sanitizePermitHeader(permitHeader: PERMIT_HEADER): any {
    return this.sanitizeForDB('PERMIT_HEADER', permitHeader);
  }

  /**
   * Get the list of valid database fields for a table
   * @param tableName - The database table name
   * @returns Array of valid field names
   */
  getValidFields(tableName: string): string[] {
    return this.DB_SCHEMA_FIELDS[tableName] || [];
  }

  /**
   * Check if a field is valid for a specific table
   * @param tableName - The database table name
   * @param fieldName - The field name to check
   * @returns True if field is valid for the table
   */
  isValidField(tableName: string, fieldName: string): boolean {
    const allowedFields = this.DB_SCHEMA_FIELDS[tableName];
    return allowedFields ? allowedFields.includes(fieldName) : false;
  }

  /**
   * Get display-only fields that are not in database schema
   * @param tableName - The database table name
   * @param dataObject - The object to check
   * @returns Array of field names that are display-only
   */
  getDisplayOnlyFields(tableName: string, dataObject: any): string[] {
    const allowedFields = this.DB_SCHEMA_FIELDS[tableName];
    if (!allowedFields) return [];

    const objectFields = Object.keys(dataObject);
    return objectFields.filter(field => !allowedFields.includes(field));
  }
}
