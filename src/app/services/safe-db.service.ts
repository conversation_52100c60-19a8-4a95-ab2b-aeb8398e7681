import { Injectable } from '@angular/core';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DbSanitizationService } from './db-sanitization.service';

@Injectable({
  providedIn: 'root'
})
export class SafeDbService {

  constructor(
    private unviredSDK: UnviredCordovaSDK,
    private dbSanitizationService: DbSanitizationService
  ) { }

  /**
   * Safe database insert that automatically sanitizes data
   * @param tableName - Database table name
   * @param dataObject - Object to insert
   * @param autoSave - Whether to auto-save
   * @returns Database result
   */
  async dbInsert(tableName: string, dataObject: any, autoSave: boolean = false) {
    try {
      const sanitizedData = this.dbSanitizationService.sanitizeForDB(tableName, dataObject);

      // Log the fields that were removed for debugging
      const removedFields = this.dbSanitizationService.getDisplayOnlyFields(tableName, dataObject);
      if (removedFields.length > 0) {
        console.log(`SafeDbService: Removed display-only fields from ${tableName}:`, removedFields);
      }

      return await this.unviredSDK.dbInsert(tableName, sanitizedData, autoSave);
    } catch (error) {
      console.error(`SafeDbService: Error inserting into ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Safe database insert or update that automatically sanitizes data
   * @param tableName - Database table name
   * @param dataObject - Object to insert/update
   * @param autoSave - Whether to auto-save
   * @returns Database result
   */
  async dbInsertOrUpdate(tableName: string, dataObject: any, autoSave: boolean = false) {
    const sanitizedData = this.dbSanitizationService.sanitizeForDB(tableName, dataObject);
    return await this.unviredSDK.dbInsertOrUpdate(tableName, sanitizedData, autoSave);
  }

  /**
   * Safe database update that automatically sanitizes data
   * @param tableName - Database table name
   * @param dataObject - Object to update
   * @param whereCondition - WHERE clause condition
   * @returns Database result
   */
  async safeDbUpdate(tableName: string, dataObject: any, whereCondition: string) {
    const sanitizedData = this.dbSanitizationService.sanitizeForDB(tableName, dataObject);
    return await this.unviredSDK.dbUpdate(tableName, sanitizedData, whereCondition);
  }

  /**
   * Regular database operations (pass-through methods)
   */
  async dbExecuteStatement(query: string) {
    return await this.unviredSDK.dbExecuteStatement(query);
  }
}
