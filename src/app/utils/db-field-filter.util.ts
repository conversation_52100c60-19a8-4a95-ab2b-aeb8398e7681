/**
 * Utility functions for filtering database fields
 */

/**
 * Display-only fields that should be excluded from database operations
 */
const DISPLAY_ONLY_FIELDS = {
  PERMIT_HEADER: [
    'FORMATTED_START_DATE',
    'FORMATTED_END_DATE', 
    'TYPE_DESC',
    'HAS_EXTENSION',
    'isShowDetailsButton',
    'isShowReviseButton',
    'permitTypeInfo'
  ]
};

/**
 * Remove display-only fields from an object before database operations
 * @param tableName - The database table name
 * @param dataObject - The object to filter
 * @returns Filtered object without display-only fields
 */
export function excludeDisplayFields(tableName: string, dataObject: any): any {
  const fieldsToExclude = DISPLAY_ONLY_FIELDS[tableName] || [];
  const filtered = { ...dataObject };
  
  fieldsToExclude.forEach(field => {
    if (filtered.hasOwnProperty(field)) {
      delete filtered[field];
    }
  });
  
  return filtered;
}

/**
 * Check if a field is a display-only field
 * @param tableName - The database table name
 * @param fieldName - The field name to check
 * @returns True if field is display-only
 */
export function isDisplayOnlyField(tableName: string, fieldName: string): boolean {
  const displayFields = DISPLAY_ONLY_FIELDS[tableName] || [];
  return displayFields.includes(fieldName);
}
