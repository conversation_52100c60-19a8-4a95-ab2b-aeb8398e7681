// .versionBlock {
//   position: relative;
//   display: flex;
//   justify-content: flex-end;
//   align-items: center;
//   width: 100%;
// }

// .version {
//   font-size: 13px;
//   padding-right: 2%;
//   margin-bottom: 2%;
//   color: grey;
// }

// .mainBlock {
//   display: flex;
//   flex-direction: column;
//   justify-content: space-between;
//   height: 100%;
// }

// a {
//   text-decoration: none;
//   color: #3f51b5;
// }

// a:hover {
//   background-color: transparent;
// }

// .textCenter {
//   text-align: center;
// }

.main-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.carmeuse-logo {
  width: 230px;
}

.project-heading {
  margin: 2% 0;
  font-weight: 600;
}

.login-button {
  width: 230px;
  margin-bottom: 2%;
}

// Login page background fix
:host {
  background-color: white;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

.mainBlock {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100vh;
  background-color: white;
  width: 100%;
}

.versionBlock {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.version {
  font-size: 12px;
  margin-bottom: 1%;
  color: slategrey;
  font-family: unset;
}
