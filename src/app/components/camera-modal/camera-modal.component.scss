.camera-modal-layout {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
}

.top-controls {
  flex: 0.5;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 100001;
  min-height: 60px;

  .close-btn {
    --background: transparent;
    --color: white;
    width: 50px;
    height: 50px;
    
    ion-icon {
      color: white;
      font-size: 28px;
    }
  }

  .camera-actions {
    display: flex;
    gap: 15px;

    ion-button {
      --background: transparent;
      --color: white;
      width: 50px;
      height: 50px;
      
      ion-icon {
        color: white;
        font-size: 24px;
      }
    }
  }
}

.camera-preview-area {
  flex: 14;
  background: black;
  position: relative;
}

.bottom-controls {
  flex: 1;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100001;
  min-height: 80px;
}

.capture-button {
  --background: transparent !important;
  --border-radius: 50% !important;
  width: 60px !important;
  height: 60px !important;
  margin: 0 !important;
  padding: 0 !important;
  background: transparent !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.capture-circle {
  width: 50px;
  height: 50px;
  min-width: 50px;
  min-height: 50px;
  max-width: 50px;
  max-height: 50px;
  border: 3px solid white;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
  aspect-ratio: 1/1;
}

.capture-button:active .capture-circle {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.7);
}