<div class="create-permit-modal">
<ion-header class="modal-header">
  <ion-toolbar color="primary">
    <ion-title style="font-weight: 600;font-size: large;padding-inline: 0px;">Create New Permit</ion-title>
    <ion-buttons slot="end" mode="ios">
      <ion-button (click)="cancel()" size="small">
        <ion-icon slot="icon-only" name="close-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<div class="modal-content">
  <form [formGroup]="createForm">
    <ion-card style="box-shadow: none !important;border-radius: 8px;border: 1px solid gainsboro;">
      <ion-grid>
        <ion-row>
          <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
            <ion-select mode="md" style="--padding-start: 16px !important;min-height: 43px;" interface="popover"
              fill="outline" formControlName="permitType" required="true" labelPlacement="stacked">
              <div slot="label">Select Permit Type <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="apps-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
              <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
              <ion-select-option *ngFor="let permit of permitTypesList"
                [value]="permit.PERMIT_TYPE">{{permit.DESCRIPTION}}</ion-select-option>
            </ion-select>
          </ion-col>
          <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
            <ion-input mode="md" labelPlacement="floating" style="--padding-start: 16px !important;min-height: 43px;"
              formControlName="jobNumber" fill="outline"  maxlength="20">
              <div slot="label">Enter Job Number</div>
              <ion-icon slot="start" name="document-text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
            <ion-input mode="md" style="--padding-start: 16px !important;min-height: 43px;" labelPlacement="floating"
              formControlName="description" required="true" fill="outline" maxlength="40">
              <div slot="label">Enter Description <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
          </ion-col>
          <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
            <ion-select mode="md" style="--padding-start: 16px !important;min-height: 43px;" interface="popover"
              fill="outline" formControlName="facility" required="true" label-placement="stacked">
              <div slot="label">Select Facility <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="business-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
              <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
              <ion-select-option *ngFor="let facility of facilitiesList"
                [value]="facility.FACILITY_ID">{{facility.FACILITY_ID}} - {{facility.NAME}}</ion-select-option>
            </ion-select>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
            <ion-select mode="md" (ionChange)="divisionChange()"
              style="--padding-start: 16px !important;min-height: 43px;" interface="popover" fill="outline"
              formControlName="division" required="true" label-placement="stacked">
              <div slot="label">Select Division <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="layers-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
              <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
              <ion-select-option *ngFor="let division of divisionsList"
                [value]="division.DIVISION_ID">{{division.NAME}}</ion-select-option>
            </ion-select>
          </ion-col>
          <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
            <ion-select mode="md" style="--padding-start: 16px !important;min-height: 43px;" interface="popover"
              fill="outline" formControlName="structureTag" required="true" label-placement="stacked">
              <div slot="label">Select Structure Tag <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="construct-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
              <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
              <ion-select-option *ngFor="let structure of structuresArray"
                [value]="structure.TAG">{{structure.NAME}}</ion-select-option>
            </ion-select>
          </ion-col>
        </ion-row>

        <ion-row>
          <!-- Start Date -->
          <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
            <div style="position: relative;">
              <label style="position: absolute; top: -8px; left: 16px; background: white; padding: 0 4px; font-size: 12px; color: #666; z-index: 1;">
                Start Date <span style="color: #eb445a;">*</span>
              </label>
              <input
                style="width: 100%; padding: 12px 16px 12px 45px; border: 1px solid var(--ion-color-step-300, #b3b3b3); border-radius: 4px; height: 43px; background: transparent; font-size: 14px; box-sizing: border-box; font-family: inherit;"
                placeholder="Select Start Date"
                readonly
                [owlDateTime]="startDatePicker"
                [owlDateTimeTrigger]="startDatePicker"
                formControlName="startDate">
              <ion-icon name="calendar-outline" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #00629b; pointer-events: none;"></ion-icon>
            </div>
            <owl-date-time #startDatePicker [pickerType]="'both'" [hour12Timer]="true"
              (afterPickerClosed)="onStartDateChange($event)"></owl-date-time>
          </ion-col>

          <!-- End Date -->
          <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
            <div style="position: relative;">
              <label style="position: absolute; top: -8px; left: 16px; background: white; padding: 0 4px; font-size: 12px; color: #666; z-index: 1;">
                End Date <span style="color: #eb445a;">*</span>
              </label>
              <input
                style="width: 100%; padding: 12px 16px 12px 45px; border: 1px solid var(--ion-color-step-300, #b3b3b3); border-radius: 4px; height: 43px; background: transparent; font-size: 14px; box-sizing: border-box; font-family: inherit;"
                placeholder="Select End Date"
                readonly
                [owlDateTime]="endDatePicker"
                [owlDateTimeTrigger]="endDatePicker"
                formControlName="endDate">
              <ion-icon name="calendar-outline" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #00629b; pointer-events: none;"></ion-icon>
            </div>
            <owl-date-time #endDatePicker [pickerType]="'both'" [hour12Timer]="true"
              [min]="minEndDate" [max]="maxEndDate"
              (afterPickerClosed)="onEndDateChange($event)"></owl-date-time>
            <ion-note style="font-size: 12px; color: #666; margin-top: 4px; display: block;">
              Maximum 8 hours from start date
            </ion-note>
            <!-- Validation error message -->
            <ion-text color="danger" style="font-size: 12px; margin-top: 4px; display: block;"
              *ngIf="createForm.get('endDate')?.invalid && createForm.get('endDate')?.touched">
              <span *ngIf="createForm.get('endDate')?.errors?.['maxDuration']">
                End date cannot be more than 8 hours from start date
              </span>
              <span *ngIf="createForm.get('endDate')?.errors?.['minDate']">
                End date cannot be before start date
              </span>
            </ion-text>
          </ion-col>
        </ion-row>
        

        <ion-row>
          <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
            <ion-select mode="md" (ionChange)="getUsers()" style="--padding-start: 16px !important;min-height: 43px;"
              interface="popover" fill="outline" formControlName="agentInternal" required="true" label-placement="stacked">
              <div slot="label">Select Agent Internal <ion-text color="danger">*</ion-text> </div>
              <ion-icon slot="start" name="person-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
              <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
              <ion-select-option *ngFor="let internal of agentsInternalList"
                [value]="internal.AGENT_ID">{{internal.NAME}}</ion-select-option>
            </ion-select>
          </ion-col>
          <ion-col size="12" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
            <ion-select mode="md" (ionChange)="getUsers()" style="--padding-start: 16px !important;min-height: 43px;"
              interface="popover" fill="outline" formControlName="agentExternal" label-placement="stacked">
              <div slot="label">Select Agent External </div>
              <ion-icon slot="start" name="people-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
              <ion-select-option value="" class="selectClearOption">Select</ion-select-option>
              <ion-select-option *ngFor="let external of agentsExternalList"
                [value]="external.AGENT_ID">{{external.NAME}}</ion-select-option>
            </ion-select>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col size="12">
            <ion-textarea label="Comments" formControlName="comments" maxlength="1000" labelPlacement="floating" fill="outline"
              placeholder="Enter Comments">
              <ion-icon slot="start" name="chatbox-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-textarea>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card>

  </form>

  <div style="margin-top: 2%;text-align: center;">
    <ion-text style="color: indianred;" class="textCenter" *ngIf="errorMessage.length > 0">
      <span>{{errorMessage}}</span>
    </ion-text>
  </div>
</div>

<ion-footer class="modal-footer" mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="cancel()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md" [disabled]="!createForm.valid" (click)="save()">Create</ion-button>
  </ion-toolbar>
</ion-footer>
</div>