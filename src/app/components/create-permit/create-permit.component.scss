ion-toolbar {
    padding-inline: 10px;
    padding-top: 0px;
    padding-bottom: 0px;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 0.0125em;

  }
  
  .refreshButton {
    // --border-color: #868686 !important;
    --border-width: 1px !important;
    --border-radius: 8px !important;
    font-weight: 600 !important;
  }

  .date-control {
    --border-color: var(--ion-color-step-300, #b3b3b3);
    --border-radius: 4px;
    --border-width: 1px;
    --min-height: 43px;
  }

  ion-col {
    margin-top: 10px;
  }

  .textCenter {
    text-align: center;
  }

  // Mobile-specific layout and keyboard handling
  @media (max-width: 768px) {
    // Mobile modal structure using CSS Grid
    .create-permit-modal {
      display: grid;
      grid-template-rows: auto 1fr auto;
      height: 100vh;
      max-height: 100vh;
    }

    .modal-header {
      grid-row: 1;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .modal-content {
      grid-row: 2;
      overflow-y: auto;
      padding: 16px;

      // Enable smooth scrolling for keyboard handling
      scroll-behavior: smooth;
    }

    .modal-footer {
      grid-row: 3;
      position: sticky;
      bottom: 0;
      z-index: 10;
    }

    // Remove excessive form padding
    form {
      padding-bottom: 20px; // Much less padding than before
    }

    // Ensure input fields scroll into view when focused
    ion-input, ion-textarea, ion-select {
      &:focus-within {
        scroll-margin-top: 20px;
        scroll-margin-bottom: 100px; // Space for keyboard
      }
    }

    // Specific handling for textarea (comments field)
    ion-textarea {
      &:focus-within {
        scroll-margin-bottom: 120px;
      }
    }
  }

  // Desktop layout - normal behavior
  @media (min-width: 769px) {
    .create-permit-modal {
      display: block; // Normal block layout for desktop
    }

    .modal-content {
      padding: 16px;
    }
  }

.date-popover-content {
  width: fit-content !important;
}

/* Angular DateTime Picker Custom Styling */
:host ::ng-deep {
  .owl-dt-popup {
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .owl-dt-container {
    border-radius: 8px;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell {
    border-radius: 4px;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell.owl-dt-calendar-cell-selected {
    background-color: var(--ion-color-primary);
    color: white;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell.owl-dt-calendar-cell-today {
    border: 2px solid var(--ion-color-primary);
  }

  .owl-dt-timer-box {
    border-radius: 4px;
    border: 1px solid var(--ion-color-step-300, #b3b3b3);
  }

  .owl-dt-control-button {
    background-color: var(--ion-color-primary);
    color: white;
    border-radius: 4px;
    border: none;
    padding: 8px 16px;
    font-weight: 500;
  }

  .owl-dt-control-button:hover {
    background-color: var(--ion-color-primary-shade);
  }

  .owl-dt-container-buttons {
    border-top: 1px solid var(--ion-color-step-200, #e0e0e0);
    padding: 12px 16px;
  }
}