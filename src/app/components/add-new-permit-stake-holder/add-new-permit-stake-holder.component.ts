import { Component, Input, OnInit } from '@angular/core';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ModalController } from '@ionic/angular';
import { PERMIT_STAKEHOLDER, skillsData } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import { PermitStatus, PermitUserRole } from 'src/app/shared/app-constants';
import { SkillsCertificateViewComponent } from '../skills-certificate-view/skills-certificate-view.component';
import { AttachmentsService } from 'src/app/services/attachments.service';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-add-new-permit-stake-holder',
  templateUrl: './add-new-permit-stake-holder.component.html',
  styleUrls: ['./add-new-permit-stake-holder.component.scss'],
})
export class AddNewPermitStakeHolderComponent implements OnInit {
  // rolesList: string[] = [
  //   PermitUserRole.APPROVE,
  //   PermitUserRole.EXECUTE,
  //   PermitUserRole.ISSUE,
  //   PermitUserRole.EXTEND,
  //   PermitUserRole.CLOSE,
  //   PermitUserRole.CANCEL,
  //   PermitUserRole.REPORT,
  // ];
  rolesList: string[] = [];
  usersList: any[] = [];

  selectedRole: string = '';
  selectedUser: string = '';
  comment: string = '';
  skillsData: skillsData[] = []
  usersSkills = []
  searchTerm: string = '';
  initialSkillData: skillsData[] = []
  usersData: any = []
  public isMobile: boolean = false;

  @Input() stakeData: any;
  @Input() editPartner: boolean;
  @Input() permit: any;
  @Input() stakeholderList: any
  @Input() isPermitRevised: boolean;
  internalUsers = [];
  userDocsData = [];
  skillsList: any[] = []; // Array to store skill descriptions

  constructor(
    private modalController: ModalController,
    private unviredSDK: UnviredCordovaSDK,
    public dataservice: DataService,
    public attachmentService: AttachmentsService,
    public sanitizer: DomSanitizer
  ) { 
    const devicePlatform = this.dataservice.getDevicePlatform();
    this.isMobile = devicePlatform === 'browser' ? false : true;
     }

   async ionViewWillEnter() {

    // const stakeholderUsers = this.stakeData.users;
    console.log('this.stakeData.users' , this.stakeData.users)
    if(this.stakeData.users && this.stakeData.users.length > 0){
this.stakeData.users = this.stakeData.users.filter(
  (user, index, self) =>
    index === self.findIndex(u => u.USER_ID === user.USER_ID)
);
    }

// console.log('Unique users',stakeholderUsers);
    
    if(this.stakeData.users == undefined ){
      if(this.stakeData.ROLE != undefined){
        this.selectedRole = this.stakeData.ROLE            
        this.roleChange();
      }
     
      // this.stakeData.users = this.usersList
      console.log("stake data users in " , this.stakeData.users)
      await this.getSkills(); // Load skills data for descriptions
      await this.getUserSkillsDataFromDB()
    } 

 
    let uData: any = await this.dataservice.getAllUserData()
    if(uData.type == ResultType.success){
      this.usersData = uData.data.USER

     this.internalUsers = this.usersData
  .filter(user => user.USER_HEADER && user.USER_HEADER.IS_INTERNAL === "true")
  .map(user => user.USER_HEADER);


    }
   
    

    if(this.editPartner){
    // Loop through each stakeholder user
    if (this.stakeData.users && this.stakeData.users.length > 0) {
      this.stakeData.users.forEach((user: any) => {
        const fullName = `${user.FIRST_NAME} ${user.LAST_NAME || ''}`;
        const username = fullName.toLowerCase().replace(/ /g, "_");
  
        // Add the user to skillsData if not already present
        const existingEntry = this.skillsData.find((entry) => entry.username === username);
        if (!existingEntry) {
          
          this.getUserSkillsDataFromDB()
          this.skillsData.push({
            user_id: user.USER_ID,
            username: fullName,
            External:  this.internalUsers.some(internalUser => internalUser.USER_ID === user.USER_ID)  ? "False" : "True",
            skills: [] // Initialize with an empty skills array
          });
        }
      });
    }
    

    this.initialSkillData = this.skillsData

    console.log("this.skillsData is " , this.skillsData)
    } else {
      
      let fullName ;
      let username ;
      if(this.stakeData != undefined && this.stakeData.USER_ID != undefined){
        const matchingUser = this.stakeData.users.find(user => 
          user.USER_ID === this.stakeData.USER_ID
        );

        if (matchingUser) {
          fullName = `${matchingUser.FIRST_NAME} ${matchingUser.LAST_NAME}`;
          username = fullName.toLowerCase().replace(/ /g, "_");
          console.log("matching user is", matchingUser);
          await this.getSkills(); // Load skills data for descriptions
          await this.getUserSkillsDataFromDB();
        }
      }
     
      
    
    


    }

  }

  async ngOnInit() {
   
    console.log('Received data:', this.stakeData , this.permit ); // Use the data here
    switch (this.permit?.STATUS) {
      case PermitStatus.IN_REVIEW:
        let countQuery = `SELECT COUNT(ROLE) AS COUNT FROM PERMIT_STAKEHOLDER WHERE ROLE='APPROVE' AND PERMIT_NO= '${this.permit.PERMIT_NO}'`;
        let countResult = await this.unviredSDK.dbExecuteStatement(countQuery);
        const isRevised = this.stakeholderList?.filter(stake => stake.ROLE == 'REVIEW' && stake.APPR_TYPE == null).length
        console.log("isRevised" , isRevised)

       
        if (countResult?.data[0]?.COUNT > 0 ) {
          if(isRevised > 1){
            const hasRevisedApproveRole = this.stakeholderList.filter(stakeholder => stakeholder.ROLE === 'APPROVE').length
            if(isRevised == hasRevisedApproveRole){
              this.rolesList = [PermitUserRole.EXECUTE];
            }else{
              this.rolesList = [PermitUserRole.EXECUTE, PermitUserRole.APPROVE];
            }
          } else {
            this.rolesList = [PermitUserRole.EXECUTE];
          }
          
        } else {
          const hasApproveRole = this.stakeholderList?.some(stakeholder => stakeholder.ROLE === 'APPROVE');
          
        
        
          if(isRevised > 1){
            const hasRevisedApproveRole = this.stakeholderList.filter(stakeholder => stakeholder.ROLE === 'APPROVE').length
            if(isRevised == hasRevisedApproveRole){
              this.rolesList = [PermitUserRole.EXECUTE];
            }else{
              this.rolesList = [PermitUserRole.EXECUTE, PermitUserRole.APPROVE];
            }
          }else if (hasApproveRole) {
        this.rolesList = [PermitUserRole.EXECUTE];
      } else {
        this.rolesList = [PermitUserRole.EXECUTE, PermitUserRole.APPROVE];
      }
      break;
        }
        break;
      case PermitStatus.APPROVED:
        this.rolesList = [PermitUserRole.ISSUE];
      
        break;
      case PermitStatus.ISSUED:
        this.rolesList = [PermitUserRole.CLOSE];
        break;
    }

  }

  async cancel() {
    try {
      // First set any component state that needs to be cleared
      this.selectedRole = '';
      this.selectedUser = '';
      this.comment = '';
      
      // Then dismiss the modal with specific data
      await this.modalController.dismiss({
        dismissed: true,
        role: 'cancel'
      }, 'cancel', 'newStakeHolderRolePopup');
  
    } catch (error) {
      console.error('Error dismissing modal:', error);
      // Force dismiss as fallback
      await this.modalController.dismiss();
    }
  }

  async create(userID: string) {
    try {
    let stakeHolder = new PERMIT_STAKEHOLDER();
    stakeHolder.P_MODE = 'A';
    stakeHolder.USER_ID = userID;
    stakeHolder.ROLE = this.selectedRole.trim();
    stakeHolder.PERMIT_NO = this.permit.PERMIT_NO;
    stakeHolder.SYNC_STATUS = 0;
    stakeHolder.OBJECT_STATUS = 1;
    stakeHolder.LID = this.unviredSDK.guid().replace(/-/g, '');
    stakeHolder.FID = this.permit.LID;
    stakeHolder.COMMENT = '';
    stakeHolder.AGENT_ID = this.permit.AGENT_ID_INT;
    stakeHolder.ROW_ID = await this.findMaxRowNumberFromDb(
      this.permit.PERMIT_NO
    );
    // All new stakeholders should be active by default
    stakeHolder.IS_ACTIVE = 'X';
    
    // stakeHolder.SEQ_NO = 0;
   
    // this.roleChange()
    await this.unviredSDK.dbInsert('PERMIT_STAKEHOLDER', stakeHolder, false);
    //  this.modalController.dismiss(true, null, 'newStakeHolderRolePopup');
      
    await this.modalController.dismiss({
      stakeHolder: stakeHolder,
      dismissed: true
    }, 'created', 'newStakeHolderRolePopup');
  } catch (error) {
    console.error('Error in create:', error);
    await this.modalController.dismiss();
  }
  }

  async roleChange() {
    let getUsersQuery = '';
    this.usersList = [];
    if (this.permit.AGENT_ID_EXT != null && this.permit.AGENT_ID_INT != null) {
      // getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,${this.selectedRole} 
      // from role_header as A join user_header as B on A.role_name=B.role_name 
      //   and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_EXT}','${this.permit.AGENT_ID_INT}')) 
      //   where ${this.selectedRole} = 'true'`;



        getUsersQuery = `
        SELECT B.first_name, B.last_name, B.USER_ID, B.role_name, ${this.selectedRole}
        FROM role_header AS A
        JOIN user_header AS B 
            ON A.role_name = B.role_name
        WHERE B.User_id IN (
            SELECT user_id 
            FROM agent_user_header 
            WHERE agent_id IN ('${this.permit.AGENT_ID_EXT}', '${this.permit.AGENT_ID_INT}')
        ) 
        AND ${this.selectedRole} = 'true';
        `;
        
    } else if (
      this.permit.AGENT_ID_EXT != null &&
      this.permit.AGENT_ID_INT == null
    ) {
      getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,${this.selectedRole} 
      from role_header as A join user_header as B on A.role_name=B.role_name 
        and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_EXT}')) 
        where ${this.selectedRole} = 'true'`;
    } else {
      getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,${this.selectedRole} 
      from role_header as A join user_header as B on A.role_name=B.role_name 
        and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_INT}')) 
        where ${this.selectedRole} = 'true'`;
    }

    let fetchApproveUsersQueryResult = await this.unviredSDK.dbExecuteStatement(
      getUsersQuery
    );
    if (fetchApproveUsersQueryResult?.data?.length > 0) {
      this.usersList = fetchApproveUsersQueryResult?.data;
        this.filterExistingUsers();
      this.skillsData = []
      await this.getSkills(); // Load skills data for descriptions
      this.displayUserSkills(this.usersList)
    }
    }




  displayUserSkills(list) {
     if (list && list.length > 0) {
      list.forEach((user: any) => {
          const fullName = `${user.FIRST_NAME} ${user.LAST_NAME || ''}`;
          const username = fullName.toLowerCase().replace(/ /g, "_");
    
          // Add the user to skillsData if not already present
          const existingEntry = this.skillsData.find((entry) => entry.username === username);
          if (!existingEntry) {
            
            this.getUserSkillsDataFromDB()
            this.skillsData.push({
              user_id: user.USER_ID,
              username: fullName,
              External:  this.internalUsers.some(internalUser => internalUser.USER_ID === user.USER_ID)  ? "False" : "True",
              skills: [] // Initialize with an empty skills array
            });

            // console.log(" this.skillsData" ,  this.skillsData)
          }
        });
      }
      this.initialSkillData = this.skillsData
        console.log(" this.skillsData" ,  this.skillsData)
  }

  async findMaxRowNumberFromDb(permitNo: any) {
    let maxNumber = 1;
    let fetchMaxLogNumberQuery = `SELECT COUNT(PERMIT_NO) as count FROM PERMIT_STAKEHOLDER WHERE PERMIT_NO ='${permitNo}'`;
    let maxLogNumberResult: any = await this.unviredSDK.dbExecuteStatement(
      fetchMaxLogNumberQuery
    );
    const maxRowId = Math.max(...this.stakeholderList.map(item => Number(item.ROW_ID)));
   console.log("maxrow id is " , maxRowId)
    if (
      maxRowId > 0 
    ) {
      maxNumber = Number(maxRowId) + 1;
    }
    return maxNumber.toString();
  }

  async viewSkillCertificate() {
    const modal = await this.modalController.create({
      cssClass: 'full-screen-modal',
      component: SkillsCertificateViewComponent,
    });
    await modal.present();
  }


  getStarArray(rating: number): string[] {
    const fullStars = Math.floor(rating);
    const halfStar = rating % 1 >= 0.5 ? 1 : 0;
    const emptyStars = 5 - fullStars - halfStar;

    return [
      ...Array(fullStars).fill('full'),
      ...Array(halfStar).fill('half'),
      ...Array(emptyStars).fill('empty')
    ];
  }

  // Load skills data for descriptions
  async getSkills() {
    this.skillsList = await this.dataservice.getData('SKILL_HEADER', null, 'SKILL_TYPE ASC');
    console.log("the skillsList in get skills is ", this.skillsList);
  }

  // Helper function to get skill description from skill type code
  getSkillDescription(skillType: string): string {
    // Check if skillsList has the skill data with descriptions
    if (this.skillsList && this.skillsList.length > 0) {
      const skill = this.skillsList.find(s => s.SKILL_TYPE === skillType);
      return skill ? skill.DESCRIPTION || skillType : skillType;
    }
    return skillType;
  }

  async getUserSkillsDataFromDB(){
    // console.log("stake data in constructor" , this.stakeData , this.stakeData.P_MODE)
    if(this.editPartner){
      if(this.stakeData.PROCESSED_ON == null){
        if (this.stakeData.users != undefined && this.stakeData.users.length > 0) {
          this.stakeData.users.forEach(async (user)=> {
            let userSkillQuery = `SELECT * FROM USER_SKILL WHERE USER_ID = '${user.USER_ID}'`;
            // console.log(userSkillQuery)
    
         
            let result = await this.unviredSDK.dbExecuteStatement(userSkillQuery);
            if(result.data.length > 0 ){
              // console.log(result.data)
              for (let i = 0; i < result.data.length; i++) {
                const userId = result.data[i].USER_ID;
                const skillType = result.data[i].SKILL_TYPE;
                const rating = result.data[i].RATING || 0; 
                this.getFilteredDocs1(skillType, userId)
                let userIndex = this.skillsData.findIndex(user => user.user_id === userId);
              
                if (userIndex === -1) {
                  // If the user doesn't exist in skillsData, create a new user entry
                 
                  this.skillsData.push({
                    user_id: userId,
                    username: user.FIRST_NAME + ' ' + (user.LAST_NAME || ''),
                    External:  this.internalUsers.some(internalUser => internalUser.USER_ID === user.USER_ID)  ? "False" : "True",
                    skills: [{ skill: skillType, rating: rating, certificates: []}]
                 
                  });
                
                  console.log("this.skillsData" , this.skillsData)
                } else {
                  // If the user already exists, check if the skill already exists for this user
                  const skillIndex = this.skillsData[userIndex].skills.findIndex(skill => skill.skill === skillType);
              
                  if (skillIndex === -1) {
                    // If the skill doesn't exist for this user, add the skill
                    this.skillsData[userIndex].skills.push({ skill: skillType, rating: rating, certificates:[] });
                    // console.log("the skills data if exists " , this.skillsData)
                  } else {
                    // If the skill already exists, update the rating if needed
                    this.skillsData[userIndex].skills[skillIndex].rating = rating; 
                    // console.log("the skills data if not exists " , this.skillsData)
                  }
                }
              }
            
            }
    
    
    
          })
          } 
          else if(this.stakeData.users == undefined){
            this.usersList.forEach(async (user)=> {
              let userSkillQuery = `SELECT * FROM USER_SKILL WHERE USER_ID = '${user.USER_ID}'`;
              // console.log(userSkillQuery)
      
           
              let result = await this.unviredSDK.dbExecuteStatement(userSkillQuery);
              if(result.data.length > 0 ){
                // console.log(result.data)
                for (let i = 0; i < result.data.length; i++) {
                  const userId = result.data[i].USER_ID;
                  const skillType = result.data[i].SKILL_TYPE;
                  const rating = result.data[i].RATING || 0; 
                  this.getFilteredDocs1(skillType, userId)
                  let userIndex = this.skillsData.findIndex(user => user.user_id === userId);
                
                  if (userIndex === -1) {
                    // If the user doesn't exist in skillsData, create a new user entry
                   
                    this.skillsData.push({
                      user_id: userId,
                      username: user.FIRST_NAME + ' ' + (user.LAST_NAME || ''),
                      External:  this.internalUsers.some(internalUser => internalUser.USER_ID === user.USER_ID)  ? "False" : "True",
                      skills: [{ skill: skillType, rating: rating, certificates: []}]
                   
                    });
                  
                    console.log("this.skillsData" , this.skillsData)
                  } else {
                    // If the user already exists, check if the skill already exists for this user
                    const skillIndex = this.skillsData[userIndex].skills.findIndex(skill => skill.skill === skillType);
                
                    if (skillIndex === -1) {
                      // If the skill doesn't exist for this user, add the skill
                      this.skillsData[userIndex].skills.push({ skill: skillType, rating: rating, certificates:[] });
                      // console.log("the skills data if exists " , this.skillsData)
                    } else {
                      // If the skill already exists, update the rating if needed
                      this.skillsData[userIndex].skills[skillIndex].rating = rating; 
                      // console.log("the skills data if not exists " , this.skillsData)
                    }
                  }
                }
              
              }
      
      
      
            })
          }
      } else{
      
          let userSkillQuery = `SELECT * FROM USER_SKILL WHERE USER_ID = '${this.stakeData.USER_ID}'`;
          // console.log(userSkillQuery)
  
       
          let result = await this.unviredSDK.dbExecuteStatement(userSkillQuery);
          if(result.data.length > 0 ){
            // console.log(result.data)
            for (let i = 0; i < result.data.length; i++) {
              const userId = result.data[i].USER_ID;
              const skillType = result.data[i].SKILL_TYPE;
              const rating = result.data[i].RATING || 0; 
              this.getFilteredDocs1(skillType, userId)
              let userIndex = this.skillsData.findIndex(user => user.user_id === userId);
              const matchingUser = this.stakeData.users.find(user => user.USER_ID === this.stakeData.USER_ID);


              if (userIndex === -1 && matchingUser) {
                const fullName = `${matchingUser.FIRST_NAME} ${matchingUser.LAST_NAME || ''}`;
                // If the user doesn't exist in skillsData, create a new user entry
                this.skillsData.push({
                  user_id: matchingUser.USER_ID,
                  username: fullName,
                  External: this.internalUsers.some(internalUser => 
                    internalUser.USER_ID === matchingUser.USER_ID
                  ) ? "False" : "True",
                  skills: [{ skill: skillType, rating: rating, certificates: []}]
                });
            
               
              
                console.log("this.skillsData" , this.skillsData)
              } else {
                // If the user already exists, check if the skill already exists for this user
                const skillIndex = this.skillsData[userIndex].skills.findIndex(skill => skill.skill === skillType);
            
                if (skillIndex === -1) {
                  // If the skill doesn't exist for this user, add the skill
                  this.skillsData[userIndex].skills.push({ skill: skillType, rating: rating, certificates:[] });
                  // console.log("the skills data if exists " , this.skillsData)
                } else {
                  // If the skill already exists, update the rating if needed
                  this.skillsData[userIndex].skills[skillIndex].rating = rating; 
                  // console.log("the skills data if not exists " , this.skillsData)
                }
              }
            }
          
          } else{
            const matchingUser = this.internalUsers.find(internalUser => 
              internalUser.USER_ID === this.stakeData.USER_ID
            );
            console.log("this.internalUsers" , this.internalUsers)
          
            this.skillsData.push({
              user_id: this.stakeData.USER_ID,
              username : `${matchingUser.FIRST_NAME} ${matchingUser.LAST_NAME || ''}`,
              External:  this.internalUsers.some(internalUser => internalUser.USER_ID === this.stakeData.USER_ID)  ? "False" : "True",
              skills: [] // Initialize with an empty skills array
            });
          }
  
  
  
      
      }
  
    } else {


      if (this.usersList.length > 0) {
        this.usersList.forEach(async (user)=> {
          let userSkillQuery = `SELECT * FROM USER_SKILL WHERE USER_ID = '${user.USER_ID}'`;
          // console.log(userSkillQuery)
  
       
          let result = await this.unviredSDK.dbExecuteStatement(userSkillQuery);
          if(result.data.length > 0 ){
            // console.log(result.data)
            for (let i = 0; i < result.data.length; i++) {
              const userId = result.data[i].USER_ID;
              const skillType = result.data[i].SKILL_TYPE;
              const rating = result.data[i].RATING || 0; 
              this.getFilteredDocs1(skillType, userId)
              let userIndex = this.skillsData.findIndex(user => user.user_id === userId);
            
              if (userIndex === -1) {
                // If the user doesn't exist in skillsData, create a new user entry
               
                this.skillsData.push({
                  user_id: userId,
                  username: user.FIRST_NAME + ' ' + (user.LAST_NAME || ''),
                  External:  this.internalUsers.some(internalUser => internalUser.USER_ID === user.USER_ID)  ? "False" : "True",
                  skills: [{ skill: skillType, rating: rating, certificates: []}]
               
                });
              
                console.log("this.skillsData" , this.skillsData)
              } else {
                // If the user already exists, check if the skill already exists for this user
                const skillIndex = this.skillsData[userIndex].skills.findIndex(skill => skill.skill === skillType);
            
                if (skillIndex === -1) {
                  // If the skill doesn't exist for this user, add the skill
                  this.skillsData[userIndex].skills.push({ skill: skillType, rating: rating, certificates:[] });
                  // console.log("the skills data if exists " , this.skillsData)
                } else {
                  // If the skill already exists, update the rating if needed
                  this.skillsData[userIndex].skills[skillIndex].rating = rating; 
                  // console.log("the skills data if not exists " , this.skillsData)
                }
              }
            }
          
          }
  
  
  
        })
        }

    }
   

      
      


  }




  async getFilteredDocs1(skillType: string, userId: string) {
  // console.log("getFilteredDocs" , skillType)
  let userDocQUERY = `SELECT * FROM document_header dh JOIN user_doc ud ON dh.DOC_ID = ud.DOC_ID WHERE ud.USER_ID ='${userId}'`;


  let userDocResult = await this.unviredSDK.dbExecuteStatement(userDocQUERY);

  if(userDocResult?.data?.length > 0){
    for (let ri = 0; ri < userDocResult?.data?.length; ri++) {
  if (
   userDocResult.data[ri].THUMBNAIL &&
    userDocResult.data[ri].THUMBNAIL != null
  ) {
    if (
      userDocResult.data[ri].THUMBNAIL.includes(
        'data:image/png;base64,'
      ) ||
      userDocResult.data[ri].THUMBNAIL.includes(
        'data:image/jpeg;base64,'
      ) ||
      userDocResult.data[ri].THUMBNAIL.includes(
        'data:image/jpg;base64,'
      )
    ) {
    } else {
      userDocResult.data[ri].THUMBNAIL =
        this.sanitizer.bypassSecurityTrustResourceUrl(
          `data:image/jpg;base64,${userDocResult.data[ri].THUMBNAIL}`
        );
    }
    this.userDocsData.push({
      thumbnail: userDocResult.data[ri].THUMBNAIL,
      file: null,
      docItem: userDocResult.data[ri],
      DOC_TYPE: userDocResult.data[ri].DOC_TYPE
    });
  } else {
    this.userDocsData.push({
      thumbnail: userDocResult.data[ri].THUMBNAIL,
      file: null,
      docItem: userDocResult.data[ri],
      DOC_TYPE: userDocResult.data[ri].DOC_TYPE
    });
  }
}


}


 await this.userDocsData.filter(image => {
    try {
      let docCtx = image?.docItem?.DOC_CTX ? JSON.parse(image.docItem.DOC_CTX) : null;
      return docCtx?.skillType === skillType;
    } catch (e) {
      console.error("Error parsing DOC_CTX:", e);
      return false;
    }
  });
  const filteredDocs = this.userDocsData



  filteredDocs.forEach(doc => {
    try {
        const docCtx = JSON.parse(doc.docItem.DOC_CTX);
        const skillType = docCtx.skillType;

        // Find the user in skillsData
        const user = this.skillsData.find(user => user.user_id === doc.docItem.USER_ID);

        if (user) {
            // Find the matching skill in the user's skills array
            const skill = user.skills.find(s => s.skill === skillType);

            if (skill) {
                // Check if the document is already present in the certificates array
                const docExists = skill.certificates.some(cert => cert.docItem.DOC_ID === doc.docItem.DOC_ID);
                
                // Only push if it doesn't already exist
                if (!docExists) {
                    skill.certificates.push(doc);
                }
            }
        }
    } catch (error) {
        console.error("Error parsing DOC_CTX:", error);
    }
});

// console.log("the skillsdata after is ",this.skillsData);



  // Find the index of the user in skillsData
  // const userIndex = this.skillsData.findIndex(user => user.user_id === userId);

  // if (userIndex !== -1) {
  //   // Find the index of the skill in the user's skills array
  //   const skillIndex = this.skillsData[userIndex].skills.findIndex(skill => skill.skill === skillType);

  //   if (skillIndex !== -1) {
  //     // Update the certificates array for the existing skill
  //     // this.skillsData[userIndex].skills[skillIndex].certificates = filteredDocs;
  //   }
  // }


  return filteredDocs; // You can return the filtered documents if needed
}
    
filterSkillsAndNamesBasedOnSearch() {
  const search = this.searchTerm.toLowerCase();
  console.log("search term " , search)
  if(search){
    this.skillsData = this.initialSkillData.filter(user => {
      // Check if username matches the search
      console.log("user.username.toLowerCase" , user.username.toLowerCase)
      const matchesUsername = user.username.toLowerCase().includes(search);

      // Check if ANY skill matches the search (both skill code and description)
      const matchesSkill = user.skills.some(skillObj => {
        const skillCodeMatch = skillObj.skill.toLowerCase().includes(search);
        const skillDescription = this.getSkillDescription(skillObj.skill);
        const skillDescMatch = skillDescription.toLowerCase().includes(search);
        return skillCodeMatch || skillDescMatch;
      });

      return matchesUsername || matchesSkill;
    });
  } else if(search == ''){

    this.skillsData = this.initialSkillData;
  }

}


async selectUser(user_id: string) {
  try {
    if (this.editPartner) {
      this.selectedRole = '';
      this.selectedUser = '';
      
      const modalElement = await this.modalController.getTop();
      if (modalElement) {
        await this.modalController.dismiss({
          user_id: user_id,
          dismissed: true
        }, 'selected');
      }
    } else {
      await this.create(user_id);
    }
  } catch (error) {
    console.error('Error dismissing modal:', error);
    // Force dismiss as fallback
    await this.modalController.dismiss();
  }
}

   openCertificate(certificate: any, event: Event) {
    event.stopPropagation(); // Prevents selectUser() from triggering
    console.log("Opening certificate:", certificate);
    // Implement logic to open/view the certificate
  }


  filterExistingUsers() {
    if (!this.usersList || !this.stakeholderList) return;
  
    this.usersList = this.usersList.filter(user => {
      // Check if user exists in stakeholderList with same role
      if(this.permit.STATUS == 'IN_REVIEW'){
        const existsInStakeholders = this.stakeholderList.some(stakeholder => 
          stakeholder.ROLE === this.selectedRole && 
          stakeholder.USER_ID === user.USER_ID && stakeholder.PROCESSED_ON == null &&
          user[this.selectedRole] === 'true'
        );
        return !existsInStakeholders;
      } else{
        const existsInStakeholders = this.stakeholderList.some(stakeholder => 
          stakeholder.ROLE === this.selectedRole && 
          stakeholder.USER_ID === user.USER_ID && stakeholder.PROCESSED_ON != null &&
          user[this.selectedRole] === 'true'
        );
        return !existsInStakeholders;
      }
      
      // Keep user only if they don't exist in stakeholders
     
    });
  }
  

  // async getUserDocsInUi(){
     
  //   // console.log("the userdocs is " , this.userDocs)
  //   // let userId : string= this.userDocs.data.USER[0].USER_HEADER.USER_ID
  //   // let userDocQUERY = `SELECT * FROM document_header dh JOIN user_doc ud ON dh.DOC_ID = ud.DOC_ID WHERE ud.USER_ID ='${userId}'`;
  //   // let userDocQUERY = `SELECT * FROM document_header dh JOIN user_doc ud ON dh.DOC_ID = ud.DOC_ID WHERE ud.USER_ID = '${userId}' AND ud.DOC_ID IS NOT NULL`;

  //   let userDocResult = await this.unviredSDK.dbExecuteStatement(userDocQUERY);
  //   console.log("userDocResult" , userDocResult)
  //   if(userDocResult?.data?.length > 0){
  //         for (let ri = 0; ri < userDocResult?.data?.length; ri++) {
  //       if (
  //        userDocResult.data[ri].THUMBNAIL &&
  //         userDocResult.data[ri].THUMBNAIL != null
  //       ) {
  //         if (
  //           userDocResult.data[ri].THUMBNAIL.includes(
  //             'data:image/png;base64,'
  //           ) ||
  //           userDocResult.data[ri].THUMBNAIL.includes(
  //             'data:image/jpeg;base64,'
  //           ) ||
  //           userDocResult.data[ri].THUMBNAIL.includes(
  //             'data:image/jpg;base64,'
  //           )
  //         ) {
  //         } else {
  //           userDocResult.data[ri].THUMBNAIL =
  //             this.sanitizer.bypassSecurityTrustResourceUrl(
  //               `data:image/jpg;base64,${userDocResult.data[ri].THUMBNAIL}`
  //             );
  //         }
  //         this.userDocsData.push({
  //           thumbnail: userDocResult.data[ri].THUMBNAIL,
  //           file: null,
  //           docItem: userDocResult.data[ri],
  //           DOC_TYPE: userDocResult.data[ri].DOC_TYPE
  //         });
  //       } else {
  //         this.userDocsData.push({
  //           thumbnail: userDocResult.data[ri].THUMBNAIL,
  //           file: null,
  //           docItem: userDocResult.data[ri],
  //           DOC_TYPE: userDocResult.data[ri].DOC_TYPE
  //         });
  //       }
  //     }

  //     console.log("this.userDocsData is" , this.userDocsData)
  //   }

  // }

  // getFilteredDocs(skillType: string ) {
           
  //   return this..filter(image => {
  //     try {
  //       let docCtx = image?.docItem?.DOC_CTX ? JSON.parse(image.docItem.DOC_CTX) : null;
  //       return docCtx?.skillType === skillType;
  //     } catch (e) {
  //       console.error("Error parsing DOC_CTX:", e);
  //       return false;
  //     }
  //   });
  // }



      
  }




  // skillsData1 =[
  //   {
  //     "username": "michael_smith",
  //     "External": "True",
  //     "skills": [
  //       {
  //         "skill": "AutoCAD",
  //         "rating": 3.7,
  //         "certificates": []
  //       },
  //       {
  //         "skill": "SolidWorks",
  //         "rating": 4,
  //         "certificates": [
  //           {
  //             "title": "Certified SolidWorks  (CSWP)",
  //             "issuedBy": "Dassault Systèmes",
  //             "issueDate": "2023-04-15"
  //           }
  //         ]
  //       },
  //       {
  //         "skill": "CNC Programming",
  //         "rating": 3.7,
  //         "certificates": [
  //           {
  //             "title": "CNC Programming",
  //             "issuedBy": "Manufacturing Institute",
  //             "issueDate": "2022-12-01"
  //           }
  //         ]
  //       },
  //     ]
  //   },
  //   {
  //     "username": "emma_johnson",
  //     "External": "True",
  //     "skills": [
  //       {
  //         "skill": "HVAC",
  //         "rating": 4,
  //         "certificates": [
  //           {
  //             "title": "HVAC Design Certification",
  //             "issuedBy": "ASHRAE",
  //             "issueDate": "2023-03-10"
  //           }
  //         ]
  //       },
  //       {
  //         "skill": "ANSYS",
  //         "rating": 4,
  //         "certificates": [
  //           {
  //             "title": "ANSYS Mechanical Simulation",
  //             "issuedBy": "ANSYS Academy",
  //             "issueDate": "2022-09-20"
  //           }
  //         ]
  //       },
  //       {
  //         "skill": "Mechanical",
  //         "rating": 3.5,
  //         "certificates": []
  //       },
  //     ]
  //   },
  //   {
  //     "username": "william_brown",
  //     "External": "True",
  //     "skills": [
  //       {
  //         "skill": "Welding",
  //         "rating": 3.6,
  //         "certificates": [
  //           {
  //             "title": "Certified Welding Inspector (CWI)",
  //             "issuedBy": "American Welding Society",
  //             "issueDate": "2023-06-01"
  //           }
  //         ]
  //       },
  //       {
  //         "skill": "Piping",
  //         "rating": 3.6,
  //         "certificates": [
  //           {
  //             "title": "Piping Design Engineering Certification",
  //             "issuedBy": "Mechanical Engineering Institute",
  //             "issueDate": "2021-11-15"
  //           }
  //         ]
  //       },
  //       {
  //         "skill": "Mechanical",
  //         "rating": 4.6,
  //         "certificates": []
  //       },
  //       {
  //         "skill": "CAD/CAM",
  //         "rating": 3.6,
  //         "certificates": []
  //       }
  //     ]
  //   },
  //   {
  //     "username": "sophia_taylor",
  //     "External": "False",
  //     "skills": [
  //       {
  //         "skill": "Thermodynamics",
  //         "rating": 4.8,
  //         "certificates": []
  //       },
  //       {
  //         "skill": "Heat",
  //         "rating": 4.8,
  //         "certificates": [
  //           {
  //             "title": "Heat Exchanger Design Professional",
  //             "issuedBy": "Engineering Design Institute",
  //             "issueDate": "2023-01-25"
  //           }
  //         ]
  //       },
  //       {
  //         "skill": "Robotics",
  //         "rating": 4.8,
  //         "certificates": []
  //       }
  //     ]
  //   },
  //   {
  //     "username": "james_miller",
  //     "External": "False",
  //     "skills": [
  //       {
  //         "skill": "Design",
  //         "rating": 4.4,
  //         "certificates": [
  //           {
  //             "title": "Gear Design Certification",
  //             "issuedBy": "Mechanical Gear Academy",
  //             "issueDate": "2023-02-18"
  //           }
  //         ]
  //       },
    
  //       {
  //         "skill": "FMechanics",
  //         "rating": 4.4,
  //         "certificates": []
  //       },
  //       {
  //         "skill": "Safety",
  //         "rating": 4.4,
  //         "certificates": [
  //           {
  //             "title": "Industrial Safety Specialist",
  //             "issuedBy": "Safety Engineers Association",
  //             "issueDate": "2022-10-05"
  //           }
  //         ]
  //       }
  //     ]
  //   },
  //   {
  //     "username": "olivia_davis",
  //     "External": "False",
  //     "skills": [
  //       {
  //         "skill": "Pressure",
  //         "rating": 4.9,
  //         "certificates": [
  //           {
  //             "title": "Pressure Vessel Design Expert",
  //             "issuedBy": "ASME",
  //             "issueDate": "2023-05-30"
  //           }
  //         ]
  //       },
  //       {
  //         "skill": "Welding",
  //         "rating": 4.9,
  //         "certificates": []
  //       },
  //       {
  //         "skill": "Quality",
  //         "rating": 4.9,
  //         "certificates": [
  //           {
  //             "title": "Certified Quality Control Engineer",
  //             "issuedBy": "Quality Engineering Academy",
  //             "issueDate": "2022-06-15"
  //           }
  //         ]
  //       },
  //     ]
  //   }
  // ]
  
  
