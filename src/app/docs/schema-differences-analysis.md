# Database Schema vs Data Classes Analysis

## Overview
This document outlines the differences found between the database schema (metadata.json) and the TypeScript data classes (data_classes.ts).

## Key Findings

### 1. PERMIT_HEADER ✅ **RESOLVED**
**Issue**: Data class had display-only fields not in database schema
- `FORMATTED_START_DATE` - Display field for formatted start date
- `FORMATTED_END_DATE` - Display field for formatted end date  
- `TYPE_DESC` - Display field for permit type description
- `HAS_EXTENSION` - Display field for extension flag
- Additional display properties like `isShowDetailsButton`, `isShowReviseButton`, `permitTypeInfo`

**Solution**: Added to DbSanitizationService to filter out these fields during database operations.

### 2. DOCUMENT_HEADER ⚠️ **MAJOR DIFFERENCES**
**Issue**: Data class has many fields NOT present in metadata.json database schema
- `URL`, `EXTERNAL_URL`, `URL_REQUIRES_AUTH`, `LOCAL_PATH`
- `NO_CACHE`, `SERVER_TIMESTAMP`
- `TAG1`, `TAG2`, `TAG3`, `TAG4`, `TAG5`
- `ATTACHMENT_STATUS`, `AUTO_DOWNLOAD`, `PARAM`, `MESSAGE`

**Database Schema Fields Only**:
- `DOC_ID`, `FOLDER_ID`, `TITLE`, `FILE_NAME`, `MIME_TYPE`, `DOC_TYPE`
- `THUMBNAIL`, `CREATED_BY`, `CREATED_ON`, `CHANGED_BY`, `CHANGED_ON`, `INACTIVE`, `P_MODE`

**Recommendation**: These extra fields in data class are likely for client-side processing and should be filtered out during database operations.

### 3. USER_CONTEXT_HEADER ⚠️ **MINOR DIFFERENCE**
**Issue**: Data class has `P_MODE` field but metadata.json does not include it
- Data class: `USER_ID`, `FIRST_NAME`, `LAST_NAME`, `EMAIL`, `PHONE`, `CURRENT_FACILITY`, `CURRENT_FACILITY_DESC`
- Metadata: Same fields but no `P_MODE`

### 4. FORM_HEADER ⚠️ **MINOR DIFFERENCE**  
**Issue**: Data class has `P_MODE` field but metadata.json does not include it
- Data class: `FORM_ID`, `FORM_VERSION`, `FORM_NAME`, `FORM_TITLE`, `FORM_DESC`, `TEMPLATE`, `CATEGORY`, `AVATAR`, `ATTRIBUTES`, `FORM_TYPE`
- Metadata: Same fields but no `P_MODE`

### 5. Structures with Perfect Match ✅
These structures have identical fields in both data classes and metadata.json:
- `AGENT_HEADER`
- `USER_HEADER` 
- `STRUCTURE_HEADER`
- `FACILITY_HEADER`
- `DIVISION_HEADER`
- `PERMIT_TYPE_HEADER`
- `APPROVAL_TYPE_HEADER`
- `SKILL_HEADER`

## Implementation Status

### ✅ Completed
- **DbSanitizationService**: Created with comprehensive schema definitions
- **SafeDbService**: Wrapper service for automatic sanitization
- **create-permit.component.ts**: Updated to use SafeDbService
- **Schema Mapping**: Added 20+ table schemas to DB_SCHEMA_FIELDS

### 📋 Recommended Next Steps
1. **Apply SafeDbService** to other components that perform database operations
2. **Test DOCUMENT_HEADER** operations to ensure proper field filtering
3. **Review P_MODE handling** for USER_CONTEXT_HEADER and FORM_HEADER
4. **Add remaining table schemas** as needed (USER_ROLE, USER_FACILITY, etc.)

## Usage Example

```typescript
// Before: Risk of database errors with extra fields
await this.unviredSDK.dbInsert('PERMIT_HEADER', permitHeader, true);

// After: Automatic sanitization with SafeDbService  
await this.safeDbService.dbInsert('PERMIT_HEADER', permitHeader, true);
```

## Benefits
- **Prevents Database Errors**: Automatically filters out invalid fields
- **Maintains Display Logic**: Keeps display-only fields in data classes
- **Centralized Management**: Single source of truth for database schemas
- **Easy Maintenance**: Simple to update when schemas change
- **Type Safety**: Maintains TypeScript benefits while ensuring database compatibility
